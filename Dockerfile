# Multi-stage Dockerfile for GKV Transport Services
# This Dockerfile can build any of the C services based on the SERVICE_NAME build arg

# STEP 1: Build executable binary
############################
FROM alpine:latest AS builder

ARG SERVICE_NAME
LABEL description="Builder container - ${SERVICE_NAME}"

RUN apk update && apk add --no-cache \ 
    autoconf build-base binutils curl clang libtool libxslt-dev linux-headers make musl-dev \ 
    zlib-dev zstd-dev lz4-dev mysql-dev curl-dev

RUN mkdir -p /Desa/${SERVICE_NAME} && mkdir -p /Desa/Common 
COPY ./transporte_enterprise/TRANSPORT_MASTER_SOURCE/Common /Desa/Common
COPY ./transporte_enterprise/TRANSPORT_MASTER_SOURCE/${SERVICE_NAME} /Desa/${SERVICE_NAME}
COPY ./transporte_enterprise/TRANSPORT_MASTER_SOURCE/Test/conf/params.cfg  /Desa/${SERVICE_NAME}/ 

RUN cd /Desa/${SERVICE_NAME} && make clean all

############################
# STEP 2: Build a small image
############################
FROM alpine:latest

ARG SERVICE_NAME
LABEL description="Run container - ${SERVICE_NAME}"

RUN apk update && apk add --no-cache tzdata\
    mariadb-connector-c libcurl nginx curl
ENV TZ=America/Santiago

RUN mkdir -p /home/<USER>/GKV/bin
RUN mkdir -p /home/<USER>/GKV/cfg
RUN mkdir -p /home/<USER>/GKV/run
RUN mkdir -p /home/<USER>/GKV/conf
RUN mkdir -p /home/<USER>/GKV/log

RUN addgroup -S apptiaxa && adduser -S apptiaxa -G apptiaxa
RUN chown -R apptiaxa:apptiaxa /home/<USER>/GKV/

# Arguments for nginx configuration and startup script
ARG NGINX_CONFIG_FILE
ARG START_SCRIPT_FILE
ARG SERVICE_PORT=3738

# Copiar configuración de NGINX y script de arranque (si se proporcionan)
COPY ${NGINX_CONFIG_FILE} /etc/nginx/http.d/default.conf

# Crear archivo estático para health check
RUN echo "OK" > /var/www/html/health

# Copiar script de arranque
COPY ${START_SCRIPT_FILE} /start-service.sh
RUN chmod +x /start-service.sh

USER apptiaxa

# Copy the compiled binary (name varies by service)
COPY --from=builder --chown=apptiaxa:apptiaxa /Desa/${SERVICE_NAME}/* /home/<USER>/GKV/bin/
COPY --from=builder --chown=apptiaxa:apptiaxa /Desa/${SERVICE_NAME}/params.cfg /home/<USER>/GKV/conf/

WORKDIR /home/<USER>/GKV/bin

# Exponer puertos del servicio y de nginx
EXPOSE ${SERVICE_PORT} 80

USER root
# Ejecutar servicio y nginx
CMD ["/start-service.sh"]
