# Multi-stage Dockerfile for GKV Transport Services
# This Dockerfile can build any of the C services based on the SERVICE_NAME build arg

# STEP 1: Build executable binary
############################
FROM alpine:latest AS builder

ARG SERVICE_NAME
LABEL description="Builder container - ${SERVICE_NAME}"

RUN apk update && apk add --no-cache \ 
    autoconf build-base binutils curl clang libtool libxslt-dev linux-headers make musl-dev \ 
    zlib-dev zstd-dev lz4-dev mysql-dev curl-dev

RUN mkdir -p /Desa/${SERVICE_NAME} && mkdir -p /Desa/Common 
COPY ./transporte_enterprise/TRANSPORT_MASTER_SOURCE/Common /Desa/Common
COPY ./transporte_enterprise/TRANSPORT_MASTER_SOURCE/${SERVICE_NAME} /Desa/${SERVICE_NAME}
COPY ./transporte_enterprise/TRANSPORT_MASTER_SOURCE/Test/conf/params.cfg  /Desa/${SERVICE_NAME}/ 

RUN cd /Desa/${SERVICE_NAME} && make clean all

############################
# STEP 2: Build a small image
############################
FROM alpine:latest

ARG SERVICE_NAME
LABEL description="Run container - ${SERVICE_NAME}"

RUN apk update && apk add --no-cache tzdata\
    mariadb-connector-c libcurl nginx curl
ENV TZ=America/Santiago

RUN mkdir -p /home/<USER>/GKV/bin
RUN mkdir -p /home/<USER>/GKV/cfg
RUN mkdir -p /home/<USER>/GKV/run
RUN mkdir -p /home/<USER>/GKV/conf
RUN mkdir -p /home/<USER>/GKV/log

RUN addgroup -S apptiaxa && adduser -S apptiaxa -G apptiaxa
RUN chown -R apptiaxa:apptiaxa /home/<USER>/GKV/

# Arguments for service configuration
ARG SERVICE_PORT=3738

# Crear directorio y archivo estático para health check
RUN mkdir -p /var/www/html && echo "OK" > /var/www/html/health

# Crear configuración nginx genérica
RUN echo "server {" > /etc/nginx/conf.d/default.conf && \
    echo "    listen 80;" >> /etc/nginx/conf.d/default.conf && \
    echo "    listen 8080;" >> /etc/nginx/conf.d/default.conf && \
    echo "    location / {" >> /etc/nginx/conf.d/default.conf && \
    echo "        proxy_pass http://localhost:\${SERVICE_PORT};" >> /etc/nginx/conf.d/default.conf && \
    echo "        proxy_set_header Host \$host;" >> /etc/nginx/conf.d/default.conf && \
    echo "        proxy_set_header X-Real-IP \$remote_addr;" >> /etc/nginx/conf.d/default.conf && \
    echo "        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;" >> /etc/nginx/conf.d/default.conf && \
    echo "        proxy_set_header X-Forwarded-Proto \$scheme;" >> /etc/nginx/conf.d/default.conf && \
    echo "    }" >> /etc/nginx/conf.d/default.conf && \
    echo "    location /health {" >> /etc/nginx/conf.d/default.conf && \
    echo "        root /var/www/html;" >> /etc/nginx/conf.d/default.conf && \
    echo "        index health;" >> /etc/nginx/conf.d/default.conf && \
    echo "    }" >> /etc/nginx/conf.d/default.conf && \
    echo "}" >> /etc/nginx/conf.d/default.conf

# Crear script de arranque genérico
RUN echo "#!/bin/bash" > /start-service.sh && \
    echo "" >> /start-service.sh && \
    echo "# Arrancar nginx en primer plano" >> /start-service.sh && \
    echo "nginx -g 'daemon off;'" >> /start-service.sh && \
    chmod +x /start-service.sh

USER apptiaxa

# Copy the compiled binary (name varies by service)
COPY --from=builder --chown=apptiaxa:apptiaxa /Desa/${SERVICE_NAME}/* /home/<USER>/GKV/bin/
COPY --from=builder --chown=apptiaxa:apptiaxa /Desa/${SERVICE_NAME}/params.cfg /home/<USER>/GKV/conf/

WORKDIR /home/<USER>/GKV/bin

# Exponer puertos del servicio y de nginx
EXPOSE ${SERVICE_PORT} 80 8080

USER root

# Crear script de arranque específico basado en SERVICE_NAME
RUN if [ "${SERVICE_NAME}" = "ListenGKV" ]; then \
        echo "#!/bin/bash" > /start-specific.sh && \
        echo "cd /home/<USER>/GKV/bin" >> /start-specific.sh && \
        echo "./listenGKV lili &" >> /start-specific.sh && \
        echo "nginx -g 'daemon off;'" >> /start-specific.sh; \
    elif [ "${SERVICE_NAME}" = "DispatchGKV" ]; then \
        echo "#!/bin/bash" > /start-specific.sh && \
        echo "cd /home/<USER>/GKV/bin" >> /start-specific.sh && \
        echo "./dispatchGKV GKV &" >> /start-specific.sh && \
        echo "nginx -g 'daemon off;'" >> /start-specific.sh; \
    elif [ "${SERVICE_NAME}" = "DispatchMO" ]; then \
        echo "#!/bin/bash" > /start-specific.sh && \
        echo "cd /home/<USER>/GKV/bin" >> /start-specific.sh && \
        echo "./dispatchMO DMO &" >> /start-specific.sh && \
        echo "nginx -g 'daemon off;'" >> /start-specific.sh; \
    elif [ "${SERVICE_NAME}" = "DispatchACK" ]; then \
        echo "#!/bin/bash" > /start-specific.sh && \
        echo "cd /home/<USER>/GKV/bin" >> /start-specific.sh && \
        echo "./dispatchACK DACK &" >> /start-specific.sh && \
        echo "nginx -g 'daemon off;'" >> /start-specific.sh; \
    elif [ "${SERVICE_NAME}" = "Maintenance" ]; then \
        echo "#!/bin/bash" > /start-specific.sh && \
        echo "cd /home/<USER>/GKV/bin" >> /start-specific.sh && \
        echo "./maintenanceSRV MAINT &" >> /start-specific.sh && \
        echo "nginx -g 'daemon off;'" >> /start-specific.sh; \
    else \
        echo "#!/bin/bash" > /start-specific.sh && \
        echo "nginx -g 'daemon off;'" >> /start-specific.sh; \
    fi && \
    chmod +x /start-specific.sh

# Ejecutar servicio y nginx
CMD ["/start-specific.sh"]
