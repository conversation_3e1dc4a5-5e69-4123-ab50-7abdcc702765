#!/bin/bash

echo "=== SCRIPT DE PRUEBA LOCAL PARA SERVICIOS GKV ==="
echo ""

# Función para probar un servicio específico
test_service() {
    local service_name=$1
    local dockerfile_path=$2
    local port=$3
    
    echo "🔧 Probando servicio: $service_name"
    echo "📁 Dockerfile: $dockerfile_path"
    echo "🔌 Puerto: $port"
    echo ""
    
    # Compilar el servicio
    echo "📦 Compilando $service_name..."
    (cd transporte_enterprise/TRANSPORT_MASTER_SOURCE/$service_name && make clean all)
    
    if [ $? -eq 0 ]; then
        echo "✅ Compilación exitosa"
    else
        echo "❌ Error en compilación"
        return 1
    fi
    
    # Verificar archivos necesarios
    echo ""
    echo "📋 Verificando archivos necesarios:"
    echo "   - Binario compilado:"
    ls -la transporte_enterprise/TRANSPORT_MASTER_SOURCE/$service_name/ | grep -E "\.(exe|out|bin|[^.]*$)" | grep -v "\.o$" | grep -v "\.c$" | grep -v "\.h$"
    
    echo "   - Dockerfile:"
    ls -la transporte_enterprise/TRANSPORT_MASTER_SOURCE/$service_name/Dockerfile.$service_name
    
    echo "   - nginx.config:"
    ls -la transporte_enterprise/TRANSPORT_MASTER_SOURCE/$service_name/nginx.config
    
    # Construir imagen Docker
    echo ""
    echo "🐳 Construyendo imagen Docker..."
    docker build -f transporte_enterprise/TRANSPORT_MASTER_SOURCE/$service_name/Dockerfile.$service_name \
                 -t gkv-$service_name:test \
                 transporte_enterprise/TRANSPORT_MASTER_SOURCE/
    
    if [ $? -eq 0 ]; then
        echo "✅ Build Docker exitoso"
    else
        echo "❌ Error en build Docker"
        return 1
    fi
    
    # Verificar contenido del contenedor
    echo ""
    echo "🔍 Verificando contenido del contenedor:"
    echo "   - Archivo /start.sh:"
    docker run --rm gkv-$service_name:test ls -la /start.sh
    
    echo "   - Contenido de /start.sh:"
    docker run --rm gkv-$service_name:test cat /start.sh
    
    echo "   - Binarios en /home/<USER>/GKV/bin:"
    docker run --rm gkv-$service_name:test ls -la /home/<USER>/GKV/bin/
    
    # Probar arranque del contenedor
    echo ""
    echo "🚀 Probando arranque del contenedor (5 segundos)..."
    docker run --rm -d --name test-$service_name -p $port:80 gkv-$service_name:test
    
    sleep 5
    
    # Verificar si el contenedor está corriendo
    if docker ps | grep -q test-$service_name; then
        echo "✅ Contenedor arrancó correctamente"
        
        # Probar health check
        echo "🏥 Probando health check:"
        curl -f http://localhost:$port/health || echo "❌ Health check falló"
        
        # Detener contenedor
        docker stop test-$service_name
    else
        echo "❌ Contenedor falló al arrancar"
        echo "📋 Logs del contenedor:"
        docker logs test-$service_name 2>/dev/null || echo "No hay logs disponibles"
    fi
    
    echo ""
    echo "=================================================="
    echo ""
}

# Crear directorio de logs
mkdir -p logs

# Probar cada servicio
echo "Iniciando pruebas de servicios GKV..."
echo ""

# Preguntar qué servicio probar
echo "¿Qué servicio quieres probar?"
echo "1) ListenGKV"
echo "2) DispatchGKV" 
echo "3) DispatchMO"
echo "4) DispatchACK"
echo "5) Maintenance"
echo "6) Todos"
echo ""
read -p "Selecciona una opción (1-6): " option

case $option in
    1)
        test_service "ListenGKV" "ListenGKV/Dockerfile.ListenGKV" "8081"
        ;;
    2)
        test_service "DispatchGKV" "DispatchGKV/Dockerfile.DispatchGKV" "8082"
        ;;
    3)
        test_service "DispatchMO" "DispatchMO/Dockerfile.DispatchMO" "8083"
        ;;
    4)
        test_service "DispatchACK" "DispatchACK/Dockerfile.DispatchACK" "8084"
        ;;
    5)
        test_service "Maintenance" "Maintenance/Dockerfile.Maintenance" "8085"
        ;;
    6)
        test_service "ListenGKV" "ListenGKV/Dockerfile.ListenGKV" "8081"
        test_service "DispatchGKV" "DispatchGKV/Dockerfile.DispatchGKV" "8082"
        test_service "DispatchMO" "DispatchMO/Dockerfile.DispatchMO" "8083"
        test_service "DispatchACK" "DispatchACK/Dockerfile.DispatchACK" "8084"
        test_service "Maintenance" "Maintenance/Dockerfile.Maintenance" "8085"
        ;;
    *)
        echo "Opción inválida"
        exit 1
        ;;
esac

echo "🎉 Pruebas completadas!"
echo ""
echo "💡 Para usar docker-compose:"
echo "   docker-compose up -d                    # Arrancar todos los servicios"
echo "   docker-compose up -d listengkv          # Arrancar solo ListenGKV"
echo "   docker-compose logs listengkv           # Ver logs de ListenGKV"
echo "   docker-compose down                     # Detener todos los servicios"
