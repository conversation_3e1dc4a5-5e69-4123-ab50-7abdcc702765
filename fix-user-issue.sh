#!/bin/bash

echo "🔧 Arreglando problema del usuario en todos los servicios..."

# Función para actualizar un servicio
fix_user_service() {
    local service=$1
    local binary=$2
    local param=$3
    
    echo "📦 Arreglando usuario en $service..."
    
    # Actualizar script de arranque para no usar su
    cat > transporte_enterprise/TRANSPORT_MASTER_SOURCE/$service/start.sh << EOF
#!/bin/bash

# Configurar variables de entorno
export LHOME=/home/<USER>/GKV

# Limpiar archivos de lock previos
rm -f /home/<USER>/GKV/run/*.pid
rm -f /home/<USER>/GKV/run/*.lock

# Crear directorios necesarios
mkdir -p /home/<USER>/GKV/{bin,cfg,run,conf,log}
chown -R apptiaxa:apptiaxa /home/<USER>/GKV/

# Verificar que el binario existe
if [ ! -f "/home/<USER>/GKV/bin/$binary" ]; then
    echo "ERROR: Binario $binary no encontrado"
    exit 1
fi

# Arrancar el servicio $service en segundo plano
echo "Iniciando servicio $service..."
cd /home/<USER>/GKV/bin

# Cambiar a usuario apptiaxa y ejecutar el servicio
runuser -u apptiaxa -- /home/<USER>/GKV/bin/$binary $param &

# Esperar un poco para que el servicio arranque
sleep 3

# Verificar que nginx puede arrancar
nginx -t
if [ \$? -ne 0 ]; then
    echo "ERROR: Configuración de nginx inválida"
    exit 1
fi

# Arrancar NGINX en primer plano
echo "Iniciando nginx..."
nginx -g 'daemon off;'
EOF
    
    chmod +x transporte_enterprise/TRANSPORT_MASTER_SOURCE/$service/start.sh
    echo "✅ $service usuario arreglado"
}

# Arreglar todos los servicios
fix_user_service "ListenGKV" "listenGKV" "lili"
fix_user_service "DispatchGKV" "dispatchGKV" "GKV"
fix_user_service "DispatchMO" "dispatchMO" "MO"
fix_user_service "DispatchACK" "dispatchACK" "ACK"
fix_user_service "Maintenance" "maintenanceSRV" "MAINT"

echo ""
echo "🎉 Problema del usuario arreglado en todos los servicios!"
echo ""
echo "💡 Ahora puedes probar:"
echo "   docker build -f transporte_enterprise/TRANSPORT_MASTER_SOURCE/ListenGKV/Dockerfile.ListenGKV -t test-listengkv:latest transporte_enterprise/TRANSPORT_MASTER_SOURCE/"
echo "   docker run --rm -d --name test-listengkv -p 8081:80 test-listengkv:latest"
echo "   curl http://localhost:8081/health"
