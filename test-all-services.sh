#!/bin/bash

echo "🧪 Probando todos los servicios GKV..."

# Función para probar un servicio
test_service() {
    local service=$1
    local image=$2
    local port=$3
    local container_name="test-$service"
    
    echo ""
    echo "🔍 Probando $service en puerto $port..."
    
    # Detener contenedor si existe
    docker stop $container_name 2>/dev/null || true
    
    # Ejecutar el contenedor
    echo "   Iniciando contenedor..."
    if docker run --rm -d --name $container_name -p $port:80 $image; then
        echo "   ✅ Contenedor iniciado"
        
        # Esperar un poco para que el servicio arranque
        echo "   ⏳ Esperando que el servicio arranque..."
        sleep 8
        
        # Probar health check
        echo "   🏥 Probando health check..."
        if curl -f -s http://localhost:$port/health > /dev/null; then
            echo "   ✅ Health check OK"
            
            # Mostrar logs del servicio
            echo "   📋 Logs del servicio:"
            docker logs $container_name | head -10
            
            return 0
        else
            echo "   ❌ Health check falló"
            echo "   📋 Logs del error:"
            docker logs $container_name
            return 1
        fi
    else
        echo "   ❌ Error iniciando contenedor"
        return 1
    fi
}

# Función para limpiar contenedores
cleanup() {
    echo ""
    echo "🧹 Limpiando contenedores de prueba..."
    docker stop test-listengkv test-dispatchgkv test-dispatchmo test-dispatchack test-maintenance 2>/dev/null || true
    echo "✅ Limpieza completada"
}

# Configurar trap para limpiar al salir
trap cleanup EXIT

# Probar todos los servicios
echo "🚀 Iniciando pruebas de servicios..."

test_service "listengkv" "gkv-listengkv:latest" "8081"
LISTENGKV_STATUS=$?

test_service "dispatchgkv" "gkv-dispatchgkv:latest" "8082"
DISPATCHGKV_STATUS=$?

test_service "dispatchmo" "gkv-dispatchmo:latest" "8083"
DISPATCHMO_STATUS=$?

test_service "dispatchack" "gkv-dispatchack:latest" "8084"
DISPATCHACK_STATUS=$?

test_service "maintenance" "gkv-maintenance:latest" "8085"
MAINTENANCE_STATUS=$?

# Resumen de resultados
echo ""
echo "📊 RESUMEN DE PRUEBAS:"
echo "======================"

if [ $LISTENGKV_STATUS -eq 0 ]; then
    echo "✅ ListenGKV    - OK (puerto 8081)"
else
    echo "❌ ListenGKV    - FALLÓ"
fi

if [ $DISPATCHGKV_STATUS -eq 0 ]; then
    echo "✅ DispatchGKV  - OK (puerto 8082)"
else
    echo "❌ DispatchGKV  - FALLÓ"
fi

if [ $DISPATCHMO_STATUS -eq 0 ]; then
    echo "✅ DispatchMO   - OK (puerto 8083)"
else
    echo "❌ DispatchMO   - FALLÓ"
fi

if [ $DISPATCHACK_STATUS -eq 0 ]; then
    echo "✅ DispatchACK  - OK (puerto 8084)"
else
    echo "❌ DispatchACK  - FALLÓ"
fi

if [ $MAINTENANCE_STATUS -eq 0 ]; then
    echo "✅ Maintenance  - OK (puerto 8085)"
else
    echo "❌ Maintenance  - FALLÓ"
fi

# Calcular total de éxitos
TOTAL_SUCCESS=$((5 - $LISTENGKV_STATUS - $DISPATCHGKV_STATUS - $DISPATCHMO_STATUS - $DISPATCHACK_STATUS - $MAINTENANCE_STATUS))

echo ""
echo "🎯 RESULTADO FINAL: $TOTAL_SUCCESS/5 servicios funcionando correctamente"

if [ $TOTAL_SUCCESS -eq 5 ]; then
    echo "🎉 ¡Todos los servicios están funcionando perfectamente!"
    echo ""
    echo "💡 Puedes acceder a los health checks en:"
    echo "   http://localhost:8081/health  # ListenGKV"
    echo "   http://localhost:8082/health  # DispatchGKV"
    echo "   http://localhost:8083/health  # DispatchMO"
    echo "   http://localhost:8084/health  # DispatchACK"
    echo "   http://localhost:8085/health  # Maintenance"
    exit 0
else
    echo "⚠️  Algunos servicios tienen problemas. Revisa los logs arriba."
    exit 1
fi
