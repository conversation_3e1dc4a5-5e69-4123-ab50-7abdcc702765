#!/bin/bash

echo "🔧 Arreglando todos los servicios GKV..."

# Función para actualizar un servicio
fix_service() {
    local service=$1
    local binary=$2
    local param=$3
    local port=$4
    
    echo "📦 Arreglando $service..."
    
    # 1. Agregar variable LHOME al Dockerfile si no existe
    if ! grep -q "ENV LHOME" transporte_enterprise/TRANSPORT_MASTER_SOURCE/$service/Dockerfile.$service; then
        sed -i '/ENV TZ=America\/Santiago/a ENV LHOME=/home/<USER>/GKV' transporte_enterprise/TRANSPORT_MASTER_SOURCE/$service/Dockerfile.$service
    fi
    
    # 2. Actualizar script de arranque
    cat > transporte_enterprise/TRANSPORT_MASTER_SOURCE/$service/start.sh << EOF
#!/bin/bash

# Configurar variables de entorno
export LHOME=/home/<USER>/GKV

# Limpiar archivos de lock previos
rm -f /home/<USER>/GKV/run/*.pid
rm -f /home/<USER>/GKV/run/*.lock

# Crear directorios necesarios
mkdir -p /home/<USER>/GKV/{bin,cfg,run,conf,log}
chown -R apptiaxa:apptiaxa /home/<USER>/GKV/

# Verificar que el binario existe
if [ ! -f "/home/<USER>/GKV/bin/$binary" ]; then
    echo "ERROR: Binario $binary no encontrado"
    exit 1
fi

# Arrancar el servicio $service en segundo plano como usuario apptiaxa
echo "Iniciando servicio $service..."
cd /home/<USER>/GKV/bin
su apptiaxa -c "cd /home/<USER>/GKV/bin && ./$binary $param" &

# Esperar un poco para que el servicio arranque
sleep 2

# Verificar que nginx puede arrancar
nginx -t
if [ \$? -ne 0 ]; then
    echo "ERROR: Configuración de nginx inválida"
    exit 1
fi

# Arrancar NGINX en primer plano
echo "Iniciando nginx..."
nginx -g 'daemon off;'
EOF
    
    chmod +x transporte_enterprise/TRANSPORT_MASTER_SOURCE/$service/start.sh
    
    # 3. Verificar que nginx.config tiene el puerto correcto
    if [ -f "transporte_enterprise/TRANSPORT_MASTER_SOURCE/$service/nginx.config" ]; then
        # Actualizar el puerto en nginx.config
        sed -i "s/proxy_pass http:\/\/localhost:[0-9]*/proxy_pass http:\/\/localhost:$port/" transporte_enterprise/TRANSPORT_MASTER_SOURCE/$service/nginx.config
    fi
    
    echo "✅ $service arreglado"
}

# Arreglar todos los servicios
fix_service "ListenGKV" "listenGKV" "lili" "3741"
fix_service "DispatchGKV" "dispatchGKV" "GKV" "3739"
fix_service "DispatchMO" "dispatchMO" "MO" "3740"
fix_service "DispatchACK" "dispatchACK" "ACK" "3738"
fix_service "Maintenance" "maintenanceSRV" "MAINT" "3742"

echo ""
echo "🎉 Todos los servicios han sido arreglados!"
echo ""
echo "💡 Ahora puedes probar:"
echo "   ./test-local.sh"
echo "   make test"
echo "   docker-compose up -d"
echo ""
echo "🌐 URLs de health check:"
echo "   - ListenGKV:   http://localhost:8081/health"
echo "   - DispatchGKV: http://localhost:8082/health"
echo "   - DispatchMO:  http://localhost:8083/health"
echo "   - DispatchACK: http://localhost:8084/health"
echo "   - Maintenance: http://localhost:8085/health"
