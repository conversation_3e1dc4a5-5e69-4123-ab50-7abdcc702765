#!/bin/bash

echo "=== DIAGNÓSTICO DEL PROBLEMA /start.sh ==="
echo ""

# Función para diagnosticar un servicio
debug_service() {
    local service=$1
    echo "🔍 Diagnosticando $service..."
    echo ""
    
    # 1. Verificar que el Dockerfile existe
    echo "📁 1. Verificando Dockerfile:"
    if [ -f "transporte_enterprise/TRANSPORT_MASTER_SOURCE/$service/Dockerfile.$service" ]; then
        echo "✅ Dockerfile existe"
    else
        echo "❌ Dockerfile NO existe"
        return 1
    fi
    
    # 2. Mostrar la sección relevante del Dockerfile
    echo ""
    echo "📋 2. Sección de creación de /start.sh en Dockerfile:"
    grep -A 15 "Crear script de arranque" transporte_enterprise/TRANSPORT_MASTER_SOURCE/$service/Dockerfile.$service || echo "❌ No se encontró la sección"
    
    # 3. Construir imagen paso a paso
    echo ""
    echo "🐳 3. Construyendo imagen con debug..."
    
    # Crear un Dockerfile temporal con debug
    cat > /tmp/debug-dockerfile-$service << EOF
# STEP 1 build executable binary
############################
FROM alpine:latest AS builder

LABEL description="Builder container - $service"

RUN apk update && apk add --no-cache \\ 
    autoconf build-base binutils curl clang libtool libxslt-dev linux-headers make musl-dev \\ 
    zlib-dev zstd-dev lz4-dev mysql-dev curl-dev

RUN mkdir -p /Desa/$service && mkdir -p /Desa/Common 
COPY ./Common /Desa/Common
COPY ./$service /Desa/$service
COPY ./Test/conf/params.cfg  /Desa/$service

RUN cd /Desa/$service && make clean all

############################
# STEP 2 build a small image
############################
FROM alpine:latest

LABEL description="Run container - $service"

RUN apk update && apk add --no-cache tzdata\\
    mariadb-connector-c libcurl nginx curl
ENV TZ=America/Santiago

RUN mkdir -p /home/<USER>/GKV/bin
RUN mkdir -p /home/<USER>/GKV/conf
RUN mkdir -p /home/<USER>/GKV/log

RUN addgroup -g 1000 apptiaxa && \\
    adduser -D -s /bin/bash -u 1000 -G apptiaxa apptiaxa

# Copiar configuración de NGINX y script de arranque
COPY ./$service/nginx.config /etc/nginx/conf.d/default.conf

# Crear directorio y archivo estático para health check
RUN mkdir -p /var/www/html && echo "OK" > /var/www/html/health

# DEBUG: Mostrar estado antes de crear script
RUN echo "=== DEBUG: Estado antes de crear /start.sh ===" && \\
    ls -la / | grep start || echo "No hay archivos start" && \\
    echo "=== Creando /start.sh ===" && \\
    echo '#!/bin/bash' > /start.sh && \\
    echo '' >> /start.sh && \\
    echo '# Arrancar el servicio $service en segundo plano' >> /start.sh && \\
    echo 'cd /home/<USER>/GKV/bin' >> /start.sh && \\
    echo './BINARY_PLACEHOLDER PARAM_PLACEHOLDER &' >> /start.sh && \\
    echo '' >> /start.sh && \\
    echo '# Arrancar NGINX en primer plano' >> /start.sh && \\
    echo 'nginx -g "daemon off;"' >> /start.sh && \\
    chmod +x /start.sh && \\
    echo "=== DEBUG: Estado después de crear /start.sh ===" && \\
    ls -la /start.sh && \\
    echo "=== Contenido de /start.sh ===" && \\
    cat /start.sh && \\
    echo "=== Verificando permisos ===" && \\
    file /start.sh

USER apptiaxa
COPY --from=builder --chown=apptiaxa:apptiaxa /Desa/$service/* /home/<USER>/GKV/bin/
COPY --from=builder --chown=apptiaxa:apptiaxa /Desa/$service/params.cfg /home/<USER>/GKV/conf/
WORKDIR /home/<USER>/GKV/bin

# Exponer puertos del servicio y de nginx
EXPOSE 80 8080

USER root

# DEBUG: Verificar estado final
RUN echo "=== DEBUG: Estado final ===" && \\
    ls -la /start.sh && \\
    cat /start.sh && \\
    echo "=== Binarios disponibles ===" && \\
    ls -la /home/<USER>/GKV/bin/

# Ejecutar servicio y nginx
CMD ["/start.sh"]
EOF

    # Reemplazar placeholders según el servicio
    case $service in
        "ListenGKV")
            sed -i 's/BINARY_PLACEHOLDER/listenGKV/g' /tmp/debug-dockerfile-$service
            sed -i 's/PARAM_PLACEHOLDER/lili/g' /tmp/debug-dockerfile-$service
            ;;
        "DispatchGKV")
            sed -i 's/BINARY_PLACEHOLDER/dispatchGKV/g' /tmp/debug-dockerfile-$service
            sed -i 's/PARAM_PLACEHOLDER/GKV/g' /tmp/debug-dockerfile-$service
            ;;
        "DispatchMO")
            sed -i 's/BINARY_PLACEHOLDER/dispatchMO/g' /tmp/debug-dockerfile-$service
            sed -i 's/PARAM_PLACEHOLDER/MO/g' /tmp/debug-dockerfile-$service
            ;;
        "DispatchACK")
            sed -i 's/BINARY_PLACEHOLDER/dispatchACK/g' /tmp/debug-dockerfile-$service
            sed -i 's/PARAM_PLACEHOLDER/ACK/g' /tmp/debug-dockerfile-$service
            ;;
        "Maintenance")
            sed -i 's/BINARY_PLACEHOLDER/maintenanceSRV/g' /tmp/debug-dockerfile-$service
            sed -i 's/PARAM_PLACEHOLDER/MAINT/g' /tmp/debug-dockerfile-$service
            ;;
    esac
    
    # Construir con el Dockerfile de debug
    echo "🔨 Construyendo imagen de debug..."
    docker build -f /tmp/debug-dockerfile-$service -t debug-$service:latest transporte_enterprise/TRANSPORT_MASTER_SOURCE/
    
    if [ $? -eq 0 ]; then
        echo "✅ Build exitoso"
        
        # 4. Probar el contenedor
        echo ""
        echo "🧪 4. Probando contenedor:"
        echo "   - Verificando /start.sh:"
        docker run --rm debug-$service:latest ls -la /start.sh
        
        echo "   - Contenido de /start.sh:"
        docker run --rm debug-$service:latest cat /start.sh
        
        echo "   - Probando ejecución de /start.sh:"
        docker run --rm debug-$service:latest /bin/bash -c "ls -la /start.sh && file /start.sh && head -1 /start.sh"
        
        # 5. Intentar arrancar el contenedor
        echo ""
        echo "🚀 5. Intentando arrancar contenedor (timeout 10s):"
        timeout 10s docker run --rm debug-$service:latest || echo "❌ Error al arrancar o timeout"
        
    else
        echo "❌ Build falló"
    fi
    
    # Limpiar
    rm -f /tmp/debug-dockerfile-$service
    
    echo ""
    echo "=================================================="
    echo ""
}

# Menú de selección
echo "¿Qué servicio quieres diagnosticar?"
echo "1) ListenGKV"
echo "2) DispatchGKV"
echo "3) DispatchMO"
echo "4) DispatchACK"
echo "5) Maintenance"
echo ""
read -p "Selecciona una opción (1-5): " option

case $option in
    1) debug_service "ListenGKV" ;;
    2) debug_service "DispatchGKV" ;;
    3) debug_service "DispatchMO" ;;
    4) debug_service "DispatchACK" ;;
    5) debug_service "Maintenance" ;;
    *) echo "Opción inválida"; exit 1 ;;
esac

echo "🎯 Diagnóstico completado!"
