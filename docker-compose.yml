version: '3.3'

services:
  # Servicio ListenGKV
  listengkv:
    build:
      context: ./transporte_enterprise/TRANSPORT_MASTER_SOURCE
      dockerfile: ListenGKV/Dockerfile.ListenGKV
    container_name: gkv-listengkv
    ports:
      - "3741:3741"  # Puerto del servicio
      - "8081:80"    # Puerto nginx
    environment:
      - TZ=America/Santiago
    volumes:
      - ./logs:/var/log/nginx
    restart: unless-stopped
    networks:
      - gkv-network

  # Servicio DispatchGKV
  dispatchgkv:
    build:
      context: ./transporte_enterprise/TRANSPORT_MASTER_SOURCE
      dockerfile: DispatchGKV/Dockerfile.DispatchGKV
    container_name: gkv-dispatchgkv
    ports:
      - "3739:3739"  # Puerto del servicio
      - "8082:80"    # Puerto nginx
    environment:
      - TZ=America/Santiago
    volumes:
      - ./logs:/var/log/nginx
    restart: unless-stopped
    networks:
      - gkv-network

  # Servicio DispatchMO
  dispatchmo:
    build:
      context: ./transporte_enterprise/TRANSPORT_MASTER_SOURCE
      dockerfile: DispatchMO/Dockerfile.DispatchMO
    container_name: gkv-dispatchmo
    ports:
      - "3740:3740"  # Puerto del servicio
      - "8083:80"    # Puerto nginx
    environment:
      - TZ=America/Santiago
    volumes:
      - ./logs:/var/log/nginx
    restart: unless-stopped
    networks:
      - gkv-network

  # Servicio DispatchACK
  dispatchack:
    build:
      context: ./transporte_enterprise/TRANSPORT_MASTER_SOURCE
      dockerfile: DispatchACK/Dockerfile.DispatchACK
    container_name: gkv-dispatchack
    ports:
      - "3738:3738"  # Puerto del servicio
      - "8084:80"    # Puerto nginx
    environment:
      - TZ=America/Santiago
    volumes:
      - ./logs:/var/log/nginx
    restart: unless-stopped
    networks:
      - gkv-network

  # Servicio Maintenance
  maintenance:
    build:
      context: ./transporte_enterprise/TRANSPORT_MASTER_SOURCE
      dockerfile: Maintenance/Dockerfile.Maintenance
    container_name: gkv-maintenance
    ports:
      - "3742:3742"  # Puerto del servicio
      - "8085:80"    # Puerto nginx
    environment:
      - TZ=America/Santiago
    volumes:
      - ./logs:/var/log/nginx
    restart: unless-stopped
    networks:
      - gkv-network

networks:
  gkv-network:
    driver: bridge

volumes:
  logs:
    driver: local
