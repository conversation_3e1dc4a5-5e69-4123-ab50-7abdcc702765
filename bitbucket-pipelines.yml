image: atlassian/default-image:2
clone:
  depth: full

definitions:
  build: &build_and_push
    name: build and push image from Dockerfile
    oidc: true
    script:
      - export AWS_WEB_IDENTITY_TOKEN_FILE="/tmp/aws-web-identity-token-file"
      - echo ${BITBUCKET_STEP_OIDC_TOKEN} > ${AWS_WEB_IDENTITY_TOKEN_FILE}
      - test -f dotenv && source dotenv

      # Instalar AWS CLI
      - curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
      - unzip awscliv2.zip
      - ./aws/install

      # Obtener credenciales temporales
      - eval $(aws sts assume-role --role-arn ${EXECUTE_ROLE_ARN} --role-session-name docker-build | jq -r '.Credentials | "export AWS_ACCESS_KEY_ID=\(.AccessKeyId)\nexport AWS_SECRET_ACCESS_KEY=\(.SecretAccessKey)\nexport AWS_SESSION_TOKEN=\(.SessionToken)\n"')

      # Obtener versión semántica
      - export SEMVER=$(docker run --rm -v $BITBUCKET_CLONE_DIR:/repo gittools/gitversion:5.12.0-alpine.3.14-6.0 /repo -output json -showvariable FullSemVer)
      - echo "${IMAGE_NAME}:${SEMVER}"

      # Construir imagen desde Dockerfile
      - echo " Decodificando certificados .pem"
      - mkdir -p ssl
      - echo "$CA_CERT_B64"     | base64 -d > ssl/ca-cert.pem
      - echo "$CLIENT_CERT_B64" | base64 -d > ssl/client-cert.pem
      - echo "$CLIENT_KEY_B64"  | base64 -d > ssl/client-key.pem
      - chmod 600 ssl/client-key.pem
      - ls -la ssl/
      - docker build -t ${IMAGE_NAME}:${SEMVER} .

      # Push a ECR
      - pipe: atlassian/aws-ecr-push-image:2.5.0
        variables:
          AWS_DEFAULT_REGION: ${ECR_REGION}
          AWS_ACCESS_KEY_ID: ${AWS_ACCESS_KEY_ID}
          AWS_SECRET_ACCESS_KEY: ${AWS_SECRET_ACCESS_KEY}
          AWS_SESSION_TOKEN: ${AWS_SESSION_TOKEN}
          IMAGE_NAME: ${IMAGE_NAME}
          TAGS: $SEMVER
          DEBUG: "true"

    services:
      - docker
    caches:
      - docker

  update_ecs_service: &update_ecs_service
    name: update ecs task
    image: atlassian/default-image:4
    oidc: true
    script:
      - test -f dotenv && source dotenv
      - bash scripts/update-task.sh

pipelines:
  custom:
    build_common_mailer_api:
      - step:
          name: generate variables
          artifacts:
            - dotenv
          script:
            - echo "export IMAGE_NAME=common-mailer-api ECR_REGION=us-east-1 AWS_ROLE_ARN=arn:aws:iam::227932815557:role/bitbucket-openid EXECUTE_ROLE_ARN=arn:aws:iam::555456986164:role/deployer" > dotenv
      - step: *build_and_push

    update_common_mailer_api:
      - variables:
          - name: installation
            default: "smscorp"
            allowed-values:
              - smscorp
          - name: env
            default: dev
            allowed-values:
              - dev
              - prd
          - name: API_TASKDEF
            default: "common-mailer-api.yml"
      - step:
          name: generate variables
          services:
            - docker
          caches:
            - docker
          script: 
            - export SEMVER=$(docker run --rm -v $BITBUCKET_CLONE_DIR:/repo gittools/gitversion:5.12.0-alpine.3.14-6.0 /repo -output json -showvariable FullSemVer)
            - echo "export IMAGE_TAG=${SEMVER}" > dotenv
            - cat scripts/envs/${installation}/${env}.env scripts/envs/${installation}/main.env >> dotenv
            - cp scripts/taskdef/${API_TASKDEF} task-template.yml
            - cp scripts/clusters/${env}-${installation}-ecs-cluster.yml cluster-config.yml
          artifacts:
            - dotenv
            - cluster-config.yml
            - task-template.yml
      - step: *update_ecs_service
