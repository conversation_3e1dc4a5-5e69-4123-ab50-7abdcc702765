#!/bin/bash

echo "🔧 Creando scripts finales compatibles con /bin/sh..."

# Función para crear el script final compatible con sh
create_sh_script() {
    local service=$1
    local binary=$2
    local param=$3
    
    echo "📦 Creando script sh para $service..."
    
    cat > transporte_enterprise/TRANSPORT_MASTER_SOURCE/$service/start.sh << 'EOF'
#!/bin/sh

# Configurar variables de entorno
export LHOME=/home/<USER>/GKV

# Limpiar archivos de lock previos
rm -f /home/<USER>/GKV/run/*.pid
rm -f /home/<USER>/GKV/run/*.lock

# Crear directorios necesarios
mkdir -p /home/<USER>/GKV/bin
mkdir -p /home/<USER>/GKV/cfg
mkdir -p /home/<USER>/GKV/run
mkdir -p /home/<USER>/GKV/conf
mkdir -p /home/<USER>/GKV/log
chown -R apptiaxa:apptiaxa /home/<USER>/GKV/

# Verificar que el binario existe
if [ ! -f "/home/<USER>/GKV/bin/BINARY_PLACEHOLDER" ]; then
    echo "ERROR: Binario BINARY_PLACEHOLDER no encontrado"
    ls -la /home/<USER>/GKV/bin/
    exit 1
fi

# Arrancar el servicio SERVICE_PLACEHOLDER en segundo plano
echo "Iniciando servicio SERVICE_PLACEHOLDER..."
cd /home/<USER>/GKV/bin

# Ejecutar el servicio directamente en background
./BINARY_PLACEHOLDER PARAM_PLACEHOLDER &
SERVICE_PID=$!

# Esperar un poco para que el servicio arranque
sleep 3

# Verificar que el servicio está corriendo
if ! kill -0 $SERVICE_PID 2>/dev/null; then
    echo "ERROR: El servicio SERVICE_PLACEHOLDER no pudo arrancar"
    exit 1
fi

echo "Servicio SERVICE_PLACEHOLDER arrancado con PID $SERVICE_PID"

# Verificar que nginx puede arrancar
nginx -t
if [ $? -ne 0 ]; then
    echo "ERROR: Configuración de nginx inválida"
    exit 1
fi

# Arrancar NGINX en primer plano
echo "Iniciando nginx..."
exec nginx -g 'daemon off;'
EOF

    # Reemplazar placeholders
    sed -i "s/BINARY_PLACEHOLDER/$binary/g" transporte_enterprise/TRANSPORT_MASTER_SOURCE/$service/start.sh
    sed -i "s/PARAM_PLACEHOLDER/$param/g" transporte_enterprise/TRANSPORT_MASTER_SOURCE/$service/start.sh
    sed -i "s/SERVICE_PLACEHOLDER/$service/g" transporte_enterprise/TRANSPORT_MASTER_SOURCE/$service/start.sh
    
    chmod +x transporte_enterprise/TRANSPORT_MASTER_SOURCE/$service/start.sh
    echo "✅ $service script sh creado"
}

# Crear scripts sh para todos los servicios
create_sh_script "ListenGKV" "listenGKV" "lili"
create_sh_script "DispatchGKV" "dispatchGKV" "GKV"
create_sh_script "DispatchMO" "dispatchMO" "MO"
create_sh_script "DispatchACK" "dispatchACK" "ACK"
create_sh_script "Maintenance" "maintenanceSRV" "MAINT"

echo ""
echo "🎉 Scripts sh finales creados para todos los servicios!"
echo ""
echo "💡 Ahora puedes probar:"
echo "   docker build -f transporte_enterprise/TRANSPORT_MASTER_SOURCE/ListenGKV/Dockerfile.ListenGKV -t working-listengkv:latest transporte_enterprise/TRANSPORT_MASTER_SOURCE/"
echo "   docker run --rm -d --name working-listengkv -p 8081:80 working-listengkv:latest"
echo "   curl http://localhost:8081/health"
echo ""
echo "🐳 O usar docker-compose:"
echo "   docker-compose up -d"
