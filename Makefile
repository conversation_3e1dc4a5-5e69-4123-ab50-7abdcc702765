# Makefile para servicios GKV
.PHONY: help build test clean up down logs

# Variables
SERVICES = listengkv dispatchgkv dispatchmo dispatchack maintenance
COMPOSE_FILE = docker-compose.yml

help: ## Mostrar esta ayuda
	@echo "Comandos disponibles para servicios GKV:"
	@echo ""
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "  \033[36m%-15s\033[0m %s\n", $$1, $$2}'
	@echo ""
	@echo "Servicios disponibles: $(SERVICES)"

build: ## Compilar todos los servicios C
	@echo "🔧 Compilando todos los servicios..."
	@for service in ListenGKV DispatchGKV DispatchMO DispatchACK Maintenance; do \
		echo "📦 Compilando $$service..."; \
		(cd transporte_enterprise/TRANSPORT_MASTER_SOURCE/$$service && make clean all) || exit 1; \
	done
	@echo "✅ Compilación completada"

test: ## Ejecutar script de prueba interactivo
	@echo "🧪 Ejecutando pruebas..."
	./test-local.sh

test-all: build ## Probar todos los servicios automáticamente
	@echo "🧪 Probando todos los servicios..."
	@for service in ListenGKV DispatchGKV DispatchMO DispatchACK Maintenance; do \
		echo "🔍 Probando $$service..."; \
		docker build -f transporte_enterprise/TRANSPORT_MASTER_SOURCE/$$service/Dockerfile.$$service \
		             -t gkv-$$service:test \
		             transporte_enterprise/TRANSPORT_MASTER_SOURCE/ || exit 1; \
		echo "✅ $$service build exitoso"; \
	done

up: ## Arrancar todos los servicios con docker-compose
	@echo "🚀 Arrancando servicios..."
	docker-compose up -d
	@echo "✅ Servicios arrancados"
	@echo "🌐 URLs disponibles:"
	@echo "  - ListenGKV:   http://localhost:8081/health"
	@echo "  - DispatchGKV: http://localhost:8082/health"
	@echo "  - DispatchMO:  http://localhost:8083/health"
	@echo "  - DispatchACK: http://localhost:8084/health"
	@echo "  - Maintenance: http://localhost:8085/health"

down: ## Detener todos los servicios
	@echo "🛑 Deteniendo servicios..."
	docker-compose down
	@echo "✅ Servicios detenidos"

logs: ## Ver logs de todos los servicios
	docker-compose logs -f

logs-%: ## Ver logs de un servicio específico (ej: make logs-listengkv)
	docker-compose logs -f $*

restart: down up ## Reiniciar todos los servicios

restart-%: ## Reiniciar un servicio específico (ej: make restart-listengkv)
	docker-compose restart $*

shell-%: ## Abrir shell en un servicio (ej: make shell-listengkv)
	docker-compose exec $* /bin/bash

clean: ## Limpiar imágenes y contenedores
	@echo "🧹 Limpiando..."
	docker-compose down --rmi all --volumes --remove-orphans
	docker system prune -f
	@echo "✅ Limpieza completada"

status: ## Mostrar estado de los servicios
	@echo "📊 Estado de los servicios:"
	docker-compose ps

health: ## Verificar health checks de todos los servicios
	@echo "🏥 Verificando health checks..."
	@for port in 8081 8082 8083 8084 8085; do \
		echo -n "Puerto $$port: "; \
		curl -s -f http://localhost:$$port/health && echo "✅ OK" || echo "❌ FAIL"; \
	done

# Comandos individuales para cada servicio
up-listengkv: ## Arrancar solo ListenGKV
	docker-compose up -d listengkv

up-dispatchgkv: ## Arrancar solo DispatchGKV
	docker-compose up -d dispatchgkv

up-dispatchmo: ## Arrancar solo DispatchMO
	docker-compose up -d dispatchmo

up-dispatchack: ## Arrancar solo DispatchACK
	docker-compose up -d dispatchack

up-maintenance: ## Arrancar solo Maintenance
	docker-compose up -d maintenance
