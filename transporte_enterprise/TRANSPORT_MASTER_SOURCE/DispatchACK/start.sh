#!/bin/sh

# Configurar variables de entorno
export LHOME=/home/<USER>/GKV

# Limpiar archivos de lock previos
rm -f /home/<USER>/GKV/run/*.pid
rm -f /home/<USER>/GKV/run/*.lock

# Crear directorios necesarios
mkdir -p /home/<USER>/GKV/bin
mkdir -p /home/<USER>/GKV/cfg
mkdir -p /home/<USER>/GKV/run
mkdir -p /home/<USER>/GKV/conf
mkdir -p /home/<USER>/GKV/log
chown -R apptiaxa:apptiaxa /home/<USER>/GKV/

# Verificar que el binario existe
if [ ! -f "/home/<USER>/GKV/bin/dispatchACK" ]; then
    echo "ERROR: Binario dispatchACK no encontrado"
    ls -la /home/<USER>/GKV/bin/
    exit 1
fi

# Arrancar el servicio DispatchACK en segundo plano
echo "Iniciando servicio DispatchACK..."
cd /home/<USER>/GKV/bin

# Ejecutar el servicio directamente en background
./dispatchACK ACK &
SERVICE_PID=$!

# Esperar un poco para que el servicio arranque
sleep 3

# Verificar que el servicio está corriendo
if ! kill -0 $SERVICE_PID 2>/dev/null; then
    echo "ERROR: El servicio DispatchACK no pudo arrancar"
    exit 1
fi

echo "Servicio DispatchACK arrancado con PID $SERVICE_PID"

# Verificar que nginx puede arrancar
nginx -t
if [ $? -ne 0 ]; then
    echo "ERROR: Configuración de nginx inválida"
    exit 1
fi

# Arrancar NGINX en primer plano
echo "Iniciando nginx..."
exec nginx -g 'daemon off;'
