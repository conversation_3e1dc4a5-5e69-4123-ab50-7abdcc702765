# STEP 1 build executable binary
############################
FROM alpine:latest AS builder

LABEL description="Builder container - dispatchACK"

RUN apk update && apk add --no-cache \ 
    autoconf build-base binutils curl clang libtool libxslt-dev linux-headers make musl-dev \ 
    zlib-dev zstd-dev lz4-dev mysql-dev curl-dev

RUN mkdir -p /Desa/DispatchACK && mkdir -p /Desa/Common
COPY ./Common /Desa/Common
COPY ./DispatchACK /Desa/DispatchACK
COPY ./Test/conf/params.cfg  /Desa/DispatchACK

RUN cd /Desa/DispatchACK && make clean all

############################
# STEP 2 build a small image
############################
FROM alpine:latest

LABEL description="Run container - dispatchACK"

RUN apk update && apk add --no-cache tzdata\
    mariadb-connector-c libcurl nginx curl
ENV TZ=America/Santiago

RUN mkdir -p /home/<USER>/GKV/bin
RUN mkdir -p /home/<USER>/GKV/cfg
RUN mkdir -p /home/<USER>/GKV/run
RUN mkdir -p /home/<USER>/GKV/conf
RUN mkdir -p /home/<USER>/GKV/log

RUN addgroup -S apptiaxa && adduser -S apptiaxa -G apptiaxa
RUN chown -R apptiaxa:apptiaxa /home/<USER>/GKV/

# Copiar configuración de NGINX y script de arranque
COPY ./DispatchACK/nginx.config /etc/nginx/conf.d/default.conf

# Crear directorio y archivo estático para health check
RUN mkdir -p /var/www/html && echo "OK" > /var/www/html/health

# Copiar script de arranque
COPY ./DispatchACK/start.sh /start.sh
RUN chmod +x /start.sh

USER apptiaxa
COPY --from=builder --chown=apptiaxa:apptiaxa /Desa/DispatchACK/dispatchACK /home/<USER>/GKV/bin/
COPY --from=builder --chown=apptiaxa:apptiaxa /Desa/DispatchACK/params.cfg /home/<USER>/GKV/conf/
WORKDIR /home/<USER>/GKV/bin

# Exponer puertos del servicio y de nginx
EXPOSE 3738 80 8080

USER root
# Ejecutar servicio y nginx
CMD ["/start.sh"]
