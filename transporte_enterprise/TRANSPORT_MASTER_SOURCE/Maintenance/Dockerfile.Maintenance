# STEP 1 build executable binary
############################
FROM alpine:latest AS builder

LABEL description="Builder container - maintenanceSRV"

RUN apk update && apk add --no-cache \ 
    autoconf build-base binutils curl clang libtool libxslt-dev linux-headers make musl-dev  \ 
    zlib-dev zstd-dev lz4-dev mysql-dev curl-dev

RUN mkdir -p /Desa/Maintenance && mkdir -p /Desa/Common 
COPY ./Common /Desa/Common
COPY ./Maintenance /Desa/Maintenance
COPY ./Test/conf/params.cfg  /Desa/Maintenance 

RUN cd /Desa/Maintenance && make clean all

############################
# STEP 2 build a small image
############################
FROM alpine:latest

LABEL description="Run container - maintenanceSRV"


RUN apk update && apk add --no-cache tzdata\
    mariadb-connector-c libcurl nginx curl
ENV TZ=America/Santiago

RUN mkdir -p /home/<USER>/GKV/bin
RUN mkdir -p /home/<USER>/GKV/cfg
RUN mkdir -p /home/<USER>/GKV/run
RUN mkdir -p /home/<USER>/GKV/conf
RUN mkdir -p /home/<USER>/GKV/log

RUN addgroup -S apptiaxa && adduser -S apptiaxa -G apptiaxa
RUN chown -R apptiaxa:apptiaxa /home/<USER>/GKV/

# Copiar configuración de NGINX y script de arranque
COPY nginx-maintenance.config /etc/nginx/conf.d/default.conf

# Crear directorio y archivo estático para health check
RUN mkdir -p /var/www/html && echo "OK" > /var/www/html/health

# Copiar script de arranque
COPY start-maintenance.sh /start-maintenance.sh
RUN chmod +x /start-maintenance.sh

USER apptiaxa
COPY --from=builder --chown=apptiaxa:apptiaxa /Desa/Maintenance/maintenanceSRV /home/<USER>/GKV/bin/
COPY --from=builder --chown=apptiaxa:apptiaxa /Desa/Maintenance/params.cfg /home/<USER>/GKV/conf/
WORKDIR /home/<USER>/GKV/bin

# Exponer puertos del servicio y de nginx
EXPOSE 3742 80

USER root
# Ejecutar servicio y nginx
CMD ["/start-maintenance.sh"]
