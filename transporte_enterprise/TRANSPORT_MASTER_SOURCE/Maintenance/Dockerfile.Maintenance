# STEP 1 build executable binary
############################
FROM alpine:latest AS builder

LABEL description="Builder container - maintenanceSRV"

RUN apk update && apk add --no-cache \ 
    autoconf build-base binutils curl clang libtool libxslt-dev linux-headers make musl-dev  \ 
    zlib-dev zstd-dev lz4-dev mysql-dev curl-dev

RUN mkdir -p /Desa/Maintenance && mkdir -p /Desa/Common
COPY ./Common /Desa/Common
COPY ./Maintenance /Desa/Maintenance
COPY ./Test/conf/params.cfg  /Desa/Maintenance

RUN cd /Desa/Maintenance && make clean all

############################
# STEP 2 build a small image
############################
FROM alpine:latest

LABEL description="Run container - maintenanceSRV"


RUN apk update && apk add --no-cache tzdata\
    mariadb-connector-c libcurl nginx curl
ENV TZ=America/Santiago
ENV LHOME=/home/<USER>/GKV

RUN mkdir -p /home/<USER>/GKV/bin
RUN mkdir -p /home/<USER>/GKV/cfg
RUN mkdir -p /home/<USER>/GKV/run
RUN mkdir -p /home/<USER>/GKV/conf
RUN mkdir -p /home/<USER>/GKV/log

RUN addgroup -S apptiaxa && adduser -S apptiaxa -G apptiaxa
RUN chown -R apptiaxa:apptiaxa /home/<USER>/GKV/

# Copiar configuración de NGINX y script de arranque
COPY ./Maintenance/nginx.config /etc/nginx/conf.d/default.conf

# Crear directorio y archivo estático para health check
RUN mkdir -p /var/www/html && echo "OK" > /var/www/html/health

# Crear script de arranque directamente en el contenedor
RUN echo '#!/bin/bash' > /start.sh && \
    echo '' >> /start.sh && \
    echo '# Arrancar el servicio Maintenance en segundo plano' >> /start.sh && \
    echo 'cd /home/<USER>/GKV/bin' >> /start.sh && \
    echo './maintenanceSRV MAINT &' >> /start.sh && \
    echo '' >> /start.sh && \
    echo '# Arrancar NGINX en primer plano' >> /start.sh && \
    echo 'nginx -g "daemon off;"' >> /start.sh && \
    chmod +x /start.sh && \
    ls -la /start.sh && \
    cat /start.sh

USER apptiaxa
COPY --from=builder --chown=apptiaxa:apptiaxa /Desa/Maintenance/maintenanceSRV /home/<USER>/GKV/bin/
COPY --from=builder --chown=apptiaxa:apptiaxa /Desa/Maintenance/params.cfg /home/<USER>/GKV/conf/
WORKDIR /home/<USER>/GKV/bin

# Exponer puertos del servicio y de nginx
EXPOSE 3742 80 8080

USER root
# Ejecutar servicio y nginx
CMD ["/bin/sh", "/start.sh"]
