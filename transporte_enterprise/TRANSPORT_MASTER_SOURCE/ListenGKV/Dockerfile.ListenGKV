############################
# STEP 1: Build the binary
############################
FROM alpine:latest AS builder

LABEL description="Builder container - listenGKV"

RUN apk update && apk add --no-cache \
    autoconf build-base binutils curl clang libtool libxslt-dev linux-headers make musl-dev \
    zlib-dev zstd-dev lz4-dev mysql-dev curl-dev

RUN mkdir -p /Desa/ListenGKV && mkdir -p /Desa/Common
COPY ./Common /Desa/Common
COPY ./ListenGKV /Desa/ListenGKV
COPY ./Test/conf/params.cfg /Desa/ListenGKV

RUN cd /Desa/ListenGKV && make clean all

############################
# STEP 2: Runtime container
############################
FROM alpine:latest

LABEL description="Runtime container - listenGKV"

RUN apk update && apk add --no-cache \
    tzdata mariadb-connector-c libcurl nginx curl

ENV TZ=America/Santiago
ENV LHOME=/home/<USER>/GKV

# Crear usuario y carpetas
RUN mkdir -p /home/<USER>/GKV/{bin,cfg,run,conf,log} \
    && addgroup -S apptiaxa \
    && adduser -S apptiaxa -G apptiaxa \
    && chown -R apptiaxa:apptiaxa /home/<USER>/GKV/

# Crear archivo de health check
RUN mkdir -p /var/www/html && echo "OK" > /var/www/html/health

# Copiar configuración de nginx para que sea reconocida correctamente
RUN mkdir -p /etc/nginx
COPY ./ListenGKV/nginx.config /etc/nginx/conf.d/default.conf

# nginx.conf base requerido para incluir conf.d/
RUN echo 'worker_processes  1;' > /etc/nginx/nginx.conf && \
    echo 'events { worker_connections  1024; }' >> /etc/nginx/nginx.conf && \
    echo 'http {' >> /etc/nginx/nginx.conf && \
    echo '    include       /etc/nginx/mime.types;' >> /etc/nginx/nginx.conf && \
    echo '    default_type  application/octet-stream;' >> /etc/nginx/nginx.conf && \
    echo '    sendfile        on;' >> /etc/nginx/nginx.conf && \
    echo '    keepalive_timeout  65;' >> /etc/nginx/nginx.conf && \
    echo '    include /etc/nginx/conf.d/*.conf;' >> /etc/nginx/nginx.conf && \
    echo '}' >> /etc/nginx/nginx.conf

# Crear script de arranque
COPY ./ListenGKV/start.sh /start.sh
RUN chmod +x /start.sh

# Copiar binarios compilados
USER apptiaxa
COPY --from=builder --chown=apptiaxa:apptiaxa /Desa/ListenGKV/listenGKV /home/<USER>/GKV/bin/
COPY --from=builder --chown=apptiaxa:apptiaxa /Desa/ListenGKV/params.cfg /home/<USER>/GKV/conf/

WORKDIR /home/<USER>/GKV/bin

# Exponer puertos
EXPOSE 3741 80 8080

USER root
CMD ["/bin/sh", "/start.sh"]
