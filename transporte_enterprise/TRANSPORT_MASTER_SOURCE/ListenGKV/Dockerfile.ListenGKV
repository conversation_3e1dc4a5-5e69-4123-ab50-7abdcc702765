# STEP 1 build executable binary
############################
FROM alpine:latest AS builder

LABEL description="Builder container - listenGKV"

RUN apk update && apk add --no-cache \ 
    autoconf build-base binutils curl clang libtool libxslt-dev linux-headers make musl-dev  \ 
    zlib-dev zstd-dev lz4-dev mysql-dev curl-dev

RUN mkdir -p /Desa/ListenGKV && mkdir -p /Desa/Common 
COPY ./Common /Desa/Common
COPY ./ListenGKV /Desa/ListenGKV
COPY ./Test/conf/params.cfg  /Desa/ListenGKV 

RUN cd /Desa/ListenGKV && make clean all

############################
# STEP 2 build a small image
############################
FROM alpine:latest

LABEL description="Run container - listenGKV"

RUN apk update && apk add --no-cache tzdata \
    mariadb-connector-c libcurl nginx curl
ENV TZ=America/Santiago

RUN mkdir -p /home/<USER>/GKV/bin
RUN mkdir -p /home/<USER>/GKV/cfg
RUN mkdir -p /home/<USER>/GKV/run
RUN mkdir -p /home/<USER>/GKV/conf
RUN mkdir -p /home/<USER>/GKV/log

RUN addgroup -S apptiaxa && adduser -S apptiaxa -G apptiaxa
RUN chown -R apptiaxa:apptiaxa /home/<USER>/GKV/

# Copiar configuración de NGINX y script de arranque
COPY nginx.config /etc/nginx/conf.d/default.conf

# Crear directorio y archivo estático para health check
RUN mkdir -p /var/www/html && echo "OK" > /var/www/html/health

# Copiar script de arranque
COPY start.sh /start.sh
RUN chmod +x /start.sh

USER apptiaxa
COPY --from=builder --chown=apptiaxa:apptiaxa /Desa/ListenGKV/listenGKV /home/<USER>/GKV/bin/
COPY --from=builder --chown=apptiaxa:apptiaxa /Desa/ListenGKV/params.cfg /home/<USER>/GKV/conf/
WORKDIR /home/<USER>/GKV/bin

# Exponer puertos del servicio y de nginx
EXPOSE 3741 80 8080

USER root
# Ejecutar servicio y nginx
CMD ["/start.sh"]
