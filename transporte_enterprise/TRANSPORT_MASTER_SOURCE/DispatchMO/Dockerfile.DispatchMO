# STEP 1 build executable binary
############################
FROM alpine:latest AS builder

LABEL description="Builder container - dispatchMO"

RUN apk update && apk add --no-cache \ 
    autoconf build-base binutils curl clang libtool libxslt-dev linux-headers make musl-dev  \ 
    zlib-dev zstd-dev lz4-dev mysql-dev curl-dev

RUN mkdir -p /Desa/DispatchMO && mkdir -p /Desa/Common 
COPY ./Common /Desa/Common
COPY ./DispatchMO /Desa/DispatchMO
COPY ./Test/conf/params.cfg  /Desa/DispatchMO 

RUN cd /Desa/DispatchMO && make clean all

############################
# STEP 2 build a small image
############################
FROM alpine:latest

LABEL description="Run container - dispatchMO"


RUN apk update && apk add --no-cache tzdata\
    mariadb-connector-c libcurl nginx curl
ENV TZ=America/Santiago

RUN mkdir -p /home/<USER>/GKV/bin
RUN mkdir -p /home/<USER>/GKV/cfg
RUN mkdir -p /home/<USER>/GKV/run
RUN mkdir -p /home/<USER>/GKV/conf
RUN mkdir -p /home/<USER>/GKV/log

RUN addgroup -S apptiaxa && adduser -S apptiaxa -G apptiaxa
RUN chown -R apptiaxa:apptiaxa /home/<USER>/GKV/

# Copiar configuración de NGINX y script de arranque
COPY nginx-dispatchmo.config /etc/nginx/http.d/default.conf

# Crear archivo estático para health check
RUN echo "OK" > /var/www/html/health

# Copiar script de arranque
COPY start-dispatchmo.sh /start-dispatchmo.sh
RUN chmod +x /start-dispatchmo.sh

USER apptiaxa
COPY --from=builder --chown=apptiaxa:apptiaxa /Desa/DispatchMO/dispatchMO /home/<USER>/GKV/bin/
COPY --from=builder --chown=apptiaxa:apptiaxa /Desa/DispatchMO/params.cfg /home/<USER>/GKV/conf/
WORKDIR /home/<USER>/GKV/bin

# Exponer puertos del servicio y de nginx
EXPOSE 3740 80

USER root
# Ejecutar servicio y nginx
CMD ["/start-dispatchmo.sh"]
