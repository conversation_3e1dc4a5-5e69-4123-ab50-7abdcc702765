#!/bin/bash

echo "🔧 Aplicando arreglo final para todos los servicios..."

# Función para crear el script final
create_final_script() {
    local service=$1
    local binary=$2
    local param=$3
    
    echo "📦 Creando script final para $service..."
    
    cat > transporte_enterprise/TRANSPORT_MASTER_SOURCE/$service/start.sh << EOF
#!/bin/bash

# Configurar variables de entorno
export LHOME=/home/<USER>/GKV

# Limpiar archivos de lock previos
rm -f /home/<USER>/GKV/run/*.pid
rm -f /home/<USER>/GKV/run/*.lock

# Crear directorios necesarios
mkdir -p /home/<USER>/GKV/{bin,cfg,run,conf,log}
chown -R apptiaxa:apptiaxa /home/<USER>/GKV/

# Verificar que el binario existe
if [ ! -f "/home/<USER>/GKV/bin/$binary" ]; then
    echo "ERROR: Binario $binary no encontrado"
    ls -la /home/<USER>/GKV/bin/
    exit 1
fi

# Arrancar el servicio $service en segundo plano
echo "Iniciando servicio $service..."
cd /home/<USER>/GKV/bin

# Ejecutar el servicio directamente en background
./$binary $param &
SERVICE_PID=\$!

# Esperar un poco para que el servicio arranque
sleep 3

# Verificar que el servicio está corriendo
if ! kill -0 \$SERVICE_PID 2>/dev/null; then
    echo "ERROR: El servicio $service no pudo arrancar"
    exit 1
fi

echo "Servicio $service arrancado con PID \$SERVICE_PID"

# Verificar que nginx puede arrancar
nginx -t
if [ \$? -ne 0 ]; then
    echo "ERROR: Configuración de nginx inválida"
    exit 1
fi

# Arrancar NGINX en primer plano
echo "Iniciando nginx..."
exec nginx -g 'daemon off;'
EOF
    
    chmod +x transporte_enterprise/TRANSPORT_MASTER_SOURCE/$service/start.sh
    echo "✅ $service script final creado"
}

# Crear scripts finales para todos los servicios
create_final_script "ListenGKV" "listenGKV" "lili"
create_final_script "DispatchGKV" "dispatchGKV" "GKV"
create_final_script "DispatchMO" "dispatchMO" "MO"
create_final_script "DispatchACK" "dispatchACK" "ACK"
create_final_script "Maintenance" "maintenanceSRV" "MAINT"

echo ""
echo "🎉 Scripts finales creados para todos los servicios!"
echo ""
echo "💡 Ahora puedes probar:"
echo "   docker build -f transporte_enterprise/TRANSPORT_MASTER_SOURCE/ListenGKV/Dockerfile.ListenGKV -t final-listengkv:latest transporte_enterprise/TRANSPORT_MASTER_SOURCE/"
echo "   docker run --rm -d --name final-listengkv -p 8081:80 final-listengkv:latest"
echo "   curl http://localhost:8081/health"
echo ""
echo "🐳 O usar docker-compose:"
echo "   docker-compose up -d"
