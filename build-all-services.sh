#!/bin/bash

echo "🚀 Construyendo todas las imágenes de servicios GKV..."

# Directorio base
BASE_DIR="transporte_enterprise/TRANSPORT_MASTER_SOURCE"

# Función para construir un servicio
build_service() {
    local service=$1
    local dockerfile=$2
    local tag=$3
    
    echo ""
    echo "🔨 Construyendo $service..."
    echo "   Dockerfile: $dockerfile"
    echo "   Tag: $tag"
    
    if docker build -f "$BASE_DIR/$service/$dockerfile" -t "$tag" "$BASE_DIR/"; then
        echo "✅ $service construido exitosamente"
    else
        echo "❌ Error construyendo $service"
        return 1
    fi
}

# Construir todos los servicios
echo "📦 Iniciando construcción de servicios..."

build_service "ListenGKV" "Dockerfile.ListenGKV" "gkv-listengkv:latest"
build_service "DispatchGKV" "Dockerfile.DispatchGKV" "gkv-dispatchgkv:latest"
build_service "DispatchMO" "Dockerfile.DispatchMO" "gkv-dispatchmo:latest"
build_service "DispatchACK" "Dockerfile.DispatchACK" "gkv-dispatchack:latest"
build_service "Maintenance" "Dockerfile.Maintenance" "gkv-maintenance:latest"

echo ""
echo "🎉 ¡Construcción completada!"
echo ""
echo "📋 Imágenes creadas:"
docker images | grep "gkv-"
echo ""
echo "🐳 Para probar los servicios:"
echo "   docker-compose up -d"
echo ""
echo "🔍 Para verificar health checks:"
echo "   curl http://localhost:8081/health  # ListenGKV"
echo "   curl http://localhost:8082/health  # DispatchGKV"
echo "   curl http://localhost:8083/health  # DispatchMO"
echo "   curl http://localhost:8084/health  # DispatchACK"
echo "   curl http://localhost:8085/health  # Maintenance"
